Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Collecting pillow
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/b5/09/29d5cd052f7566a63e5b506fac9c60526e9ecc553825551333e1e18a4858/pillow-11.2.1-cp313-cp313-macosx_11_0_arm64.whl (3.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.0/3.0 MB 34.1 MB/s eta 0:00:00
Collecting numpy
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/4f/06/7e96c57d90bebdce9918412087fc22ca9851cceaf5567a45c1f404480e9e/numpy-2.2.6-cp313-cp313-macosx_14_0_arm64.whl (5.1 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.1/5.1 MB 63.8 MB/s eta 0:00:00
Installing collected packages: pillow, numpy
Successfully installed numpy-2.2.6 pillow-11.2.1

Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Collecting opencv-python
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/05/4d/53b30a2a3ac1f75f65a59eb29cf2ee7207ce64867db47036ad61743d5a23/opencv_python-4.11.0.86-cp37-abi3-macosx_13_0_arm64.whl (37.3 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 37.3/37.3 MB 53.6 MB/s eta 0:00:00
Collecting pytesseract
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/7a/33/8312d7ce74670c9d39a532b2c246a853861120486be9443eebf048043637/pytesseract-0.3.13-py3-none-any.whl (14 kB)
Requirement already satisfied: numpy>=1.21.2 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from opencv-python) (2.2.6)
Collecting packaging>=21.3 (from pytesseract)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl (66 kB)
Requirement already satisfied: Pillow>=8.0.0 in /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages (from pytesseract) (11.2.1)
Installing collected packages: packaging, opencv-python, pytesseract
Successfully installed opencv-python-4.11.0.86 packaging-25.0 pytesseract-0.3.13

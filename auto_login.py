#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动登录程序
功能：使用Chrome浏览器自动登录指定网站，包含验证码识别
"""

import time
import io
import base64
from PIL import Image
import pytesseract
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import cv2
import numpy as np


class AutoLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.url = "https://oa.tcjyxx.cn/dzt-sso/"
        self.username = "9CF61A77778C44EDAE5FE5B9C80BFEBE"
        self.password = "*Amkeo#770304"

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # 可选：如果不想显示浏览器窗口，取消下面这行的注释
        # chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 设置用户代理
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动Chrome浏览器失败: {e}")
            return False

    def preprocess_captcha_image(self, image):
        """预处理验证码图片以提高OCR识别率"""
        # 转换为numpy数组
        img_array = np.array(image)

        # 转换为灰度图
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # 二值化处理
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

        # 去噪
        kernel = np.ones((1,1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # 放大图片
        height, width = cleaned.shape
        cleaned = cv2.resize(cleaned, (width*3, height*3), interpolation=cv2.INTER_CUBIC)

        return Image.fromarray(cleaned)

    def recognize_captcha(self, captcha_element):
        """识别验证码"""
        try:
            # 获取验证码图片
            captcha_screenshot = captcha_element.screenshot_as_png
            captcha_image = Image.open(io.BytesIO(captcha_screenshot))

            # 预处理图片
            processed_image = self.preprocess_captcha_image(captcha_image)

            # 使用tesseract识别
            # 配置tesseract参数，只识别数字和字母
            custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            captcha_text = pytesseract.image_to_string(processed_image, config=custom_config).strip()

            # 清理识别结果
            captcha_text = ''.join(c for c in captcha_text if c.isalnum())

            print(f"🔍 识别到验证码: {captcha_text}")
            return captcha_text

        except Exception as e:
            print(f"❌ 验证码识别失败: {e}")
            return ""

    def login(self):
        """执行登录流程"""
        try:
            # 打开登录页面
            print(f"🌐 正在打开网站: {self.url}")
            self.driver.get(self.url)
            time.sleep(3)

            # 查找并填写用户名
            print("📝 正在填写用户名...")
            username_selectors = [
                "input[name='username']",
                "input[id='username']",
                "input[type='text']",
                "input[placeholder*='用户名']",
                "input[placeholder*='账号']"
            ]

            username_input = None
            for selector in username_selectors:
                try:
                    username_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except TimeoutException:
                    continue

            if username_input:
                username_input.clear()
                username_input.send_keys(self.username)
                print("✅ 用户名填写完成")
            else:
                print("❌ 未找到用户名输入框")
                return False

            # 查找并填写密码
            print("🔐 正在填写密码...")
            password_selectors = [
                "input[name='password']",
                "input[id='password']",
                "input[type='password']",
                "input[placeholder*='密码']"
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if password_input:
                password_input.clear()
                password_input.send_keys(self.password)
                print("✅ 密码填写完成")
            else:
                print("❌ 未找到密码输入框")
                return False

            # 检查是否有验证码
            print("🔍 检查验证码...")
            captcha_selectors = [
                "img[src*='captcha']",
                "img[src*='verify']",
                "img[src*='code']",
                "img[alt*='验证码']",
                "img[id*='captcha']",
                "img[class*='captcha']"
            ]

            captcha_element = None
            for selector in captcha_selectors:
                try:
                    captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if captcha_element:
                print("🎯 发现验证码，正在识别...")
                captcha_text = self.recognize_captcha(captcha_element)

                if captcha_text:
                    # 查找验证码输入框
                    captcha_input_selectors = [
                        "input[name*='captcha']",
                        "input[name*='verify']",
                        "input[name*='code']",
                        "input[placeholder*='验证码']",
                        "input[id*='captcha']"
                    ]

                    captcha_input = None
                    for selector in captcha_input_selectors:
                        try:
                            captcha_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                            break
                        except NoSuchElementException:
                            continue

                    if captcha_input:
                        captcha_input.clear()
                        captcha_input.send_keys(captcha_text)
                        print("✅ 验证码填写完成")
                    else:
                        print("❌ 未找到验证码输入框")
                        return False
                else:
                    print("❌ 验证码识别失败")
                    return False
            else:
                print("ℹ️ 未发现验证码")

            # 查找并点击登录按钮
            print("🚀 正在点击登录按钮...")
            login_button_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('登录')",
                "button:contains('登陆')",
                "button:contains('Login')",
                "a:contains('登录')",
                "input[value*='登录']",
                "input[value*='登陆']"
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    if ":contains(" in selector:
                        # 使用XPath处理包含文本的选择器
                        text_content = selector.split(':contains(')[1].split(')')[0].strip("'")
                        xpath = f"//*[contains(text(), '{text_content}')]"
                        login_button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if login_button:
                login_button.click()
                print("✅ 登录按钮点击完成")

                # 等待页面跳转或登录结果
                time.sleep(5)

                # 检查登录是否成功（可以根据实际情况调整判断条件）
                current_url = self.driver.current_url
                if current_url != self.url:
                    print("🎉 登录成功！页面已跳转")
                    return True
                else:
                    print("⚠️ 登录可能失败，页面未跳转")
                    return False
            else:
                print("❌ 未找到登录按钮")
                return False

        except Exception as e:
            print(f"❌ 登录过程中发生错误: {e}")
            return False

    def run(self):
        """运行主程序"""
        print("🤖 自动登录程序启动")

        if not self.setup_driver():
            return

        try:
            success = self.login()
            if success:
                print("✅ 登录流程完成")
                input("按回车键关闭浏览器...")
            else:
                print("❌ 登录失败")
                input("按回车键关闭浏览器...")
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")


if __name__ == "__main__":
    auto_login = AutoLogin()
    auto_login.run()

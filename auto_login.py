#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动登录程序
功能：使用Chrome浏览器自动登录指定网站，包含验证码识别
"""

import time
import io
import base64
from PIL import Image
import pytesseract
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import cv2
import numpy as np


class AutoLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.url = "https://oa.tcjyxx.cn/dzt-sso/"
        self.username = "9CF61A77778C44EDAE5FE5B9C80BFEBE"
        self.password = "*Amkeo#770304"

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # 可选：如果不想显示浏览器窗口，取消下面这行的注释
        # chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 设置用户代理
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动Chrome浏览器失败: {e}")
            return False

    def preprocess_captcha_image(self, image):
        """预处理验证码图片以提高OCR识别率"""
        # 转换为numpy数组
        img_array = np.array(image)

        # 转换为灰度图
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # 二值化处理
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

        # 去噪
        kernel = np.ones((1,1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # 放大图片
        height, width = cleaned.shape
        cleaned = cv2.resize(cleaned, (width*3, height*3), interpolation=cv2.INTER_CUBIC)

        return Image.fromarray(cleaned)

    def find_login_button(self):
        """查找登录按钮的增强方法"""
        print("🔍 开始查找登录按钮...")

        # 更全面的选择器列表 - 优先查找真正的submit按钮
        selectors = [
            # 优先查找submit按钮（即使被禁用）
            "button[type='submit']",
            "input[type='submit']",

            # 查找可能被启用的按钮
            "button[class*='submit']:not([class*='unable'])",
            "button[class*='login']:not([class*='unable'])",

            # 通过文本内容查找真正的登录按钮（不是标签页）
            "//*[contains(text(), '登录') and not(contains(@class, 'option'))]",
            "//*[contains(text(), '登陆') and not(contains(@class, 'option'))]",
            "//*[contains(text(), 'Login') and not(contains(@class, 'option'))]",
            "//*[contains(text(), '确定')]",
            "//*[contains(text(), '提交')]",
            "//*[contains(text(), 'Submit')]",

            # 通过value属性查找
            "input[value*='登录']",
            "input[value*='登陆']",
            "input[value*='Login']",
            "input[value*='确定']",
            "input[value*='提交']",

            # 通过class名称查找
            "button[class*='login']",
            "button[class*='submit']",
            "button[class*='btn']",
            ".login-btn",
            ".submit-btn",
            ".btn-login",
            ".btn-submit",

            # 通过id查找
            "#login",
            "#submit",
            "#loginBtn",
            "#submitBtn",

            # 其他可能的按钮
            "button[type='button']",

            # 链接形式的登录按钮
            "a[href*='login']",
            "a[onclick*='login']",
            "a[onclick*='submit']"
        ]

        for i, selector in enumerate(selectors):
            try:
                print(f"  🔍 尝试选择器 {i+1}/{len(selectors)}: {selector}")

                if selector.startswith("//"):
                    # XPath选择器
                    element = self.driver.find_element(By.XPATH, selector)
                else:
                    # CSS选择器
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)

                # 检查元素是否可见
                if element.is_displayed():
                    element_class = element.get_attribute('class') or ''
                    element_enabled = element.is_enabled()

                    print(f"  ✅ 找到登录按钮: {selector}")
                    print(f"     按钮文本: '{element.text}'")
                    print(f"     按钮标签: {element.tag_name}")
                    print(f"     按钮class: '{element_class}'")
                    print(f"     按钮可用: {element_enabled}")

                    if element.get_attribute('value'):
                        print(f"     按钮值: '{element.get_attribute('value')}'")

                    # 对于submit按钮，即使被禁用也尝试使用
                    if element.tag_name.lower() == 'button' and element.get_attribute('type') == 'submit':
                        print(f"  🎯 找到submit按钮，尝试启用...")
                        # 尝试移除disabled属性和unable class
                        try:
                            self.driver.execute_script("""
                                arguments[0].removeAttribute('disabled');
                                arguments[0].classList.remove('unable');
                                arguments[0].classList.add('able');
                            """, element)
                            print(f"  ✅ 尝试启用submit按钮")
                        except:
                            pass
                        return element

                    # 对于其他可用按钮
                    if element_enabled:
                        return element
                    else:
                        print(f"  ⚠️ 找到按钮但被禁用: {selector}")
                        # 如果是submit类型的按钮，仍然返回它
                        if 'submit' in element_class.lower() or element.get_attribute('type') == 'submit':
                            return element
                else:
                    print(f"  ⚠️ 找到按钮但不可见: {selector}")

            except NoSuchElementException:
                continue
            except Exception as e:
                print(f"  ❌ 选择器出错 {selector}: {e}")
                continue

        # 如果没有找到，尝试查找所有可能的按钮
        print("🔍 未找到明确的登录按钮，查找所有按钮...")
        try:
            all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
            all_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit'], input[type='button']")
            all_links = self.driver.find_elements(By.TAG_NAME, "a")

            all_elements = all_buttons + all_inputs + all_links

            print(f"  📊 找到 {len(all_elements)} 个可能的按钮元素")

            for i, element in enumerate(all_elements):
                try:
                    if element.is_displayed() and element.is_enabled():
                        text = element.text.strip().lower()
                        value = (element.get_attribute('value') or '').strip().lower()
                        onclick = (element.get_attribute('onclick') or '').lower()

                        print(f"    按钮 {i+1}: 文本='{text}', 值='{value}', 点击事件='{onclick[:50]}...'")

                        # 检查是否包含登录相关关键词
                        keywords = ['登录', '登陆', 'login', '确定', '提交', 'submit']
                        if any(keyword in text or keyword in value or keyword in onclick for keyword in keywords):
                            print(f"  ✅ 找到疑似登录按钮: {element.tag_name}")
                            return element

                except Exception as e:
                    continue

        except Exception as e:
            print(f"❌ 查找所有按钮时出错: {e}")

        print("❌ 未找到任何可用的登录按钮")
        return None

    def handle_captcha(self):
        """处理验证码的增强方法"""
        print("🔍 开始处理验证码...")

        # 查找验证码图片
        captcha_img_selectors = [
            "img[src*='captcha']",
            "img[src*='verify']",
            "img[src*='code']",
            "img[alt*='验证码']",
            "img[id*='captcha']",
            "img[class*='captcha']",
            "img[id='code-img']",
            "img[id='phonecode-img']",
            "img[id='usbcode-img']"
        ]

        captcha_element = None
        for selector in captcha_img_selectors:
            try:
                captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                if captcha_element.is_displayed():
                    print(f"✅ 找到验证码图片: {selector}")
                    break
            except NoSuchElementException:
                continue

        if captcha_element:
            print("🎯 发现验证码，正在识别...")
            captcha_text = self.recognize_captcha(captcha_element)

            if captcha_text:
                # 查找验证码输入框 - 使用调试中发现的具体选择器
                captcha_input_selectors = [
                    "input[id='code-input']",  # 从调试中发现的
                    "input[id='phonecode-input']",
                    "input[id='usbcode-input']",
                    "input[id='securityverificationcode']",
                    "input[placeholder*='验证码']",
                    "input[name*='captcha']",
                    "input[name*='verify']",
                    "input[name*='code']",
                    "input[id*='captcha']"
                ]

                captcha_input = None
                for selector in captcha_input_selectors:
                    try:
                        captcha_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if captcha_input.is_displayed():
                            print(f"✅ 找到验证码输入框: {selector}")
                            break
                    except NoSuchElementException:
                        continue

                if captcha_input:
                    captcha_input.clear()
                    captcha_input.send_keys(captcha_text)
                    print("✅ 验证码填写完成")

                    # 等待一下让页面响应
                    time.sleep(2)

                    # 检查登录按钮是否变为可用
                    submit_button = None
                    try:
                        submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                        if "unable" not in (submit_button.get_attribute('class') or ''):
                            print("✅ 登录按钮已启用")
                        else:
                            print("⚠️ 登录按钮仍然禁用，可能需要其他验证")
                    except:
                        pass

                    return True
                else:
                    print("❌ 未找到验证码输入框")
                    return False
            else:
                print("❌ 验证码识别失败")
                return False
        else:
            print("ℹ️ 未发现验证码图片")
            # 检查是否有验证码输入框但没有图片（可能是其他类型的验证）
            captcha_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='验证码']")
            if captcha_inputs:
                print("⚠️ 发现验证码输入框但没有验证码图片，可能需要手动处理")
                return False
            return True

    def recognize_captcha(self, captcha_element):
        """识别验证码"""
        try:
            # 获取验证码图片
            captcha_screenshot = captcha_element.screenshot_as_png
            captcha_image = Image.open(io.BytesIO(captcha_screenshot))

            # 预处理图片
            processed_image = self.preprocess_captcha_image(captcha_image)

            # 使用tesseract识别
            # 配置tesseract参数，只识别数字和字母
            custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            captcha_text = pytesseract.image_to_string(processed_image, config=custom_config).strip()

            # 清理识别结果
            captcha_text = ''.join(c for c in captcha_text if c.isalnum())

            print(f"🔍 识别到验证码: {captcha_text}")
            return captcha_text

        except Exception as e:
            print(f"❌ 验证码识别失败: {e}")
            return ""

    def login(self):
        """执行登录流程"""
        try:
            # 打开登录页面
            print(f"🌐 正在打开网站: {self.url}")
            self.driver.get(self.url)
            time.sleep(3)

            # 查找并填写用户名
            print("📝 正在填写用户名...")
            username_selectors = [
                "input[name='username']",
                "input[id='username']",
                "input[type='text']",
                "input[placeholder*='用户名']",
                "input[placeholder*='账号']"
            ]

            username_input = None
            for selector in username_selectors:
                try:
                    username_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except TimeoutException:
                    continue

            if username_input:
                username_input.clear()
                username_input.send_keys(self.username)
                print("✅ 用户名填写完成")
            else:
                print("❌ 未找到用户名输入框")
                return False

            # 查找并填写密码
            print("🔐 正在填写密码...")
            password_selectors = [
                "input[name='password']",
                "input[id='password']",
                "input[type='password']",
                "input[placeholder*='密码']"
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if password_input:
                password_input.clear()
                password_input.send_keys(self.password)
                print("✅ 密码填写完成")
            else:
                print("❌ 未找到密码输入框")
                return False

            # 检查是否有验证码
            print("🔍 检查验证码...")
            captcha_handled = self.handle_captcha()
            if not captcha_handled:
                print("❌ 验证码处理失败")
                return False

            # 查找并点击登录按钮
            print("🚀 正在查找登录按钮...")
            login_button = self.find_login_button()

            if login_button:
                try:
                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", login_button)
                    time.sleep(1)

                    # 尝试点击按钮
                    login_button.click()
                    print("✅ 登录按钮点击完成")

                    # 等待页面跳转或登录结果
                    time.sleep(5)

                    # 检查登录是否成功（可以根据实际情况调整判断条件）
                    current_url = self.driver.current_url
                    if current_url != self.url:
                        print("🎉 登录成功！页面已跳转")
                        return True
                    else:
                        print("⚠️ 登录可能失败，页面未跳转")
                        return False

                except Exception as e:
                    print(f"❌ 点击登录按钮时发生错误: {e}")
                    # 尝试使用JavaScript点击
                    try:
                        print("🔄 尝试使用JavaScript点击...")
                        self.driver.execute_script("arguments[0].click();", login_button)
                        print("✅ JavaScript点击完成")
                        time.sleep(5)

                        current_url = self.driver.current_url
                        if current_url != self.url:
                            print("🎉 登录成功！页面已跳转")
                            return True
                        else:
                            print("⚠️ 登录可能失败，页面未跳转")
                            return False
                    except Exception as js_e:
                        print(f"❌ JavaScript点击也失败: {js_e}")
                        return False
            else:
                print("❌ 未找到登录按钮")
                return False

        except Exception as e:
            print(f"❌ 登录过程中发生错误: {e}")
            return False

    def run(self):
        """运行主程序"""
        print("🤖 自动登录程序启动")

        if not self.setup_driver():
            return

        try:
            success = self.login()
            if success:
                print("✅ 登录流程完成")
                input("按回车键关闭浏览器...")
            else:
                print("❌ 登录失败")
                input("按回车键关闭浏览器...")
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")


if __name__ == "__main__":
    auto_login = AutoLogin()
    auto_login.run()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版自动登录程序
用于诊断登录问题
"""

import time
import io
from PIL import Image
import pytesseract
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import cv2
import numpy as np


class DebugAutoLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.url = "https://oa.tcjyxx.cn/dzt-sso/"
        self.username = "9CF61A77778C44EDAE5FE5B9C80BFEBE"
        self.password = "*Amkeo#770304"
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # 显示浏览器窗口以便观察
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动Chrome浏览器失败: {e}")
            return False
    
    def save_screenshot(self, filename):
        """保存截图"""
        try:
            self.driver.save_screenshot(filename)
            print(f"📸 截图已保存: {filename}")
        except Exception as e:
            print(f"❌ 保存截图失败: {e}")
    
    def debug_page_info(self):
        """调试页面信息"""
        print("\n" + "="*50)
        print("📊 页面调试信息")
        print("="*50)
        
        try:
            print(f"当前URL: {self.driver.current_url}")
            print(f"页面标题: {self.driver.title}")
            
            # 保存截图
            self.save_screenshot("debug_current_page.png")
            
            # 查找所有表单元素
            print("\n🔍 查找页面中的所有表单元素:")
            
            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            print(f"找到 {len(inputs)} 个输入框:")
            for i, inp in enumerate(inputs):
                try:
                    inp_type = inp.get_attribute('type') or 'text'
                    inp_name = inp.get_attribute('name') or '无名称'
                    inp_id = inp.get_attribute('id') or '无ID'
                    inp_placeholder = inp.get_attribute('placeholder') or '无占位符'
                    inp_value = inp.get_attribute('value') or '无值'
                    print(f"  输入框{i+1}: type={inp_type}, name={inp_name}, id={inp_id}, placeholder={inp_placeholder}, value={inp_value}")
                except:
                    print(f"  输入框{i+1}: 无法获取信息")
            
            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            print(f"\n找到 {len(buttons)} 个button元素:")
            for i, btn in enumerate(buttons):
                try:
                    btn_text = btn.text.strip()
                    btn_type = btn.get_attribute('type') or '无类型'
                    btn_class = btn.get_attribute('class') or '无class'
                    btn_onclick = btn.get_attribute('onclick') or '无onclick'
                    btn_visible = btn.is_displayed()
                    btn_enabled = btn.is_enabled()
                    print(f"  按钮{i+1}: 文本='{btn_text}', type={btn_type}, class={btn_class}, 可见={btn_visible}, 可用={btn_enabled}")
                    if btn_onclick:
                        print(f"    onclick: {btn_onclick}")
                except:
                    print(f"  按钮{i+1}: 无法获取信息")
            
            # 查找所有submit类型的输入框
            submits = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
            print(f"\n找到 {len(submits)} 个submit输入框:")
            for i, sub in enumerate(submits):
                try:
                    sub_value = sub.get_attribute('value') or '无值'
                    sub_class = sub.get_attribute('class') or '无class'
                    sub_visible = sub.is_displayed()
                    sub_enabled = sub.is_enabled()
                    print(f"  Submit{i+1}: value='{sub_value}', class={sub_class}, 可见={sub_visible}, 可用={sub_enabled}")
                except:
                    print(f"  Submit{i+1}: 无法获取信息")
            
            # 查找所有链接
            links = self.driver.find_elements(By.TAG_NAME, "a")
            login_links = [link for link in links if any(keyword in (link.text or '').lower() for keyword in ['登录', '登陆', 'login'])]
            print(f"\n找到 {len(login_links)} 个可能的登录链接:")
            for i, link in enumerate(login_links):
                try:
                    link_text = link.text.strip()
                    link_href = link.get_attribute('href') or '无href'
                    link_onclick = link.get_attribute('onclick') or '无onclick'
                    link_visible = link.is_displayed()
                    print(f"  链接{i+1}: 文本='{link_text}', href={link_href}, 可见={link_visible}")
                    if link_onclick:
                        print(f"    onclick: {link_onclick}")
                except:
                    print(f"  链接{i+1}: 无法获取信息")
            
            # 查找包含登录文本的所有元素
            print(f"\n🔍 查找包含'登录'文本的所有元素:")
            login_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '登录') or contains(text(), '登陆')]")
            for i, elem in enumerate(login_elements):
                try:
                    elem_tag = elem.tag_name
                    elem_text = elem.text.strip()
                    elem_class = elem.get_attribute('class') or '无class'
                    elem_onclick = elem.get_attribute('onclick') or '无onclick'
                    elem_visible = elem.is_displayed()
                    elem_enabled = elem.is_enabled()
                    print(f"  元素{i+1}: 标签={elem_tag}, 文本='{elem_text}', class={elem_class}, 可见={elem_visible}, 可用={elem_enabled}")
                    if elem_onclick:
                        print(f"    onclick: {elem_onclick}")
                except:
                    print(f"  元素{i+1}: 无法获取信息")
            
        except Exception as e:
            print(f"❌ 调试页面信息时出错: {e}")
    
    def interactive_login(self):
        """交互式登录调试"""
        try:
            # 打开登录页面
            print(f"🌐 正在打开网站: {self.url}")
            self.driver.get(self.url)
            time.sleep(3)
            
            # 调试页面信息
            self.debug_page_info()
            
            print("\n" + "="*50)
            print("🎯 开始交互式登录调试")
            print("="*50)
            
            # 等待用户确认
            input("\n按回车键继续填写用户名...")
            
            # 填写用户名
            print("📝 正在填写用户名...")
            username_filled = False
            username_selectors = [
                "input[name='username']",
                "input[id='username']", 
                "input[type='text']",
                "input[placeholder*='用户名']",
                "input[placeholder*='账号']"
            ]
            
            for selector in username_selectors:
                try:
                    username_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    username_input.clear()
                    username_input.send_keys(self.username)
                    print(f"✅ 用户名填写完成 (使用选择器: {selector})")
                    username_filled = True
                    break
                except TimeoutException:
                    continue
            
            if not username_filled:
                print("❌ 未找到用户名输入框")
                return False
            
            input("\n按回车键继续填写密码...")
            
            # 填写密码
            print("🔐 正在填写密码...")
            password_filled = False
            password_selectors = [
                "input[name='password']",
                "input[id='password']",
                "input[type='password']",
                "input[placeholder*='密码']"
            ]
            
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    password_input.clear()
                    password_input.send_keys(self.password)
                    print(f"✅ 密码填写完成 (使用选择器: {selector})")
                    password_filled = True
                    break
                except NoSuchElementException:
                    continue
            
            if not password_filled:
                print("❌ 未找到密码输入框")
                return False
            
            input("\n按回车键查看当前页面状态...")
            
            # 再次调试页面信息
            self.debug_page_info()
            
            input("\n按回车键尝试点击登录按钮...")
            
            # 尝试点击登录按钮
            print("🚀 正在查找并点击登录按钮...")
            
            # 手动选择要点击的元素
            login_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '登录') or contains(text(), '登陆')]")
            
            if login_elements:
                print(f"找到 {len(login_elements)} 个包含登录文本的元素，请选择:")
                for i, elem in enumerate(login_elements):
                    try:
                        elem_tag = elem.tag_name
                        elem_text = elem.text.strip()
                        elem_visible = elem.is_displayed()
                        elem_enabled = elem.is_enabled()
                        print(f"  {i+1}. 标签={elem_tag}, 文本='{elem_text}', 可见={elem_visible}, 可用={elem_enabled}")
                    except:
                        print(f"  {i+1}. 无法获取信息")
                
                choice = input(f"\n请输入要点击的元素编号 (1-{len(login_elements)}) 或按回车自动选择: ").strip()
                
                if choice.isdigit() and 1 <= int(choice) <= len(login_elements):
                    selected_element = login_elements[int(choice) - 1]
                else:
                    # 自动选择第一个可见可用的元素
                    selected_element = None
                    for elem in login_elements:
                        try:
                            if elem.is_displayed() and elem.is_enabled():
                                selected_element = elem
                                break
                        except:
                            continue
                
                if selected_element:
                    try:
                        # 滚动到元素位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", selected_element)
                        time.sleep(1)
                        
                        # 尝试点击
                        selected_element.click()
                        print("✅ 登录按钮点击完成")
                        
                        # 等待页面响应
                        time.sleep(5)
                        
                        # 检查页面变化
                        new_url = self.driver.current_url
                        print(f"点击后的URL: {new_url}")
                        
                        if new_url != self.url:
                            print("🎉 页面已跳转，可能登录成功")
                        else:
                            print("⚠️ 页面未跳转，检查是否有错误信息")
                            
                        # 再次调试页面
                        self.debug_page_info()
                        
                    except Exception as e:
                        print(f"❌ 点击登录按钮失败: {e}")
                        # 尝试JavaScript点击
                        try:
                            print("🔄 尝试JavaScript点击...")
                            self.driver.execute_script("arguments[0].click();", selected_element)
                            print("✅ JavaScript点击完成")
                            time.sleep(5)
                            
                            new_url = self.driver.current_url
                            print(f"JavaScript点击后的URL: {new_url}")
                            
                        except Exception as js_e:
                            print(f"❌ JavaScript点击也失败: {js_e}")
                else:
                    print("❌ 没有找到可用的登录元素")
            else:
                print("❌ 未找到包含登录文本的元素")
            
            input("\n按回车键结束调试...")
            return True
            
        except Exception as e:
            print(f"❌ 交互式登录过程中发生错误: {e}")
            return False
    
    def run(self):
        """运行调试程序"""
        print("🔍 自动登录调试程序启动")
        
        if not self.setup_driver():
            return
        
        try:
            self.interactive_login()
        finally:
            if self.driver:
                input("按回车键关闭浏览器...")
                self.driver.quit()
                print("🔚 浏览器已关闭")


if __name__ == "__main__":
    debug_login = DebugAutoLogin()
    debug_login.run()

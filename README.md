# 自动登录程序

这是一个使用Python和Selenium开发的自动登录程序，能够自动打开Chrome浏览器，访问指定网站并完成登录流程，包括自动识别验证码。

## 功能特性

- ✅ 自动打开Chrome浏览器
- ✅ 自动填写用户名和密码
- ✅ 自动识别并填写验证码
- ✅ 自动点击登录按钮
- ✅ 智能元素定位（支持多种选择器）
- ✅ 验证码图像预处理和OCR识别

## 系统要求

- Python 3.7+
- Chrome浏览器（最新版本）
- Tesseract OCR

## 安装步骤

### 1. 安装Python依赖

```bash
# 方法1: 使用安装脚本（推荐）
python setup.py

# 方法2: 手动安装
pip install -r requirements.txt
```

### 2. 安装Tesseract OCR

#### macOS
```bash
# 使用Homebrew安装
brew install tesseract
```

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr
```

#### Windows
1. 下载安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装后将安装路径添加到系统PATH环境变量
3. 默认路径：`C:\Program Files\Tesseract-OCR`

### 3. 验证安装

```bash
# 检查Tesseract是否安装成功
tesseract --version

# 检查Python依赖
python -c "import selenium, cv2, pytesseract; print('所有依赖安装成功')"
```

## 使用方法

### 基本使用

```bash
python auto_login.py
```

### 程序配置

如需修改登录信息，请编辑 `auto_login.py` 文件中的以下变量：

```python
self.url = "https://oa.tcjyxx.cn/dzt-sso/"  # 登录网址
self.username = "你的用户名"                 # 用户名
self.password = "你的密码"                   # 密码
```

## 程序流程

1. **启动浏览器** - 自动启动Chrome浏览器
2. **访问网站** - 打开指定的登录页面
3. **填写用户名** - 自动定位并填写用户名
4. **填写密码** - 自动定位并填写密码
5. **处理验证码** - 如果存在验证码，自动识别并填写
6. **点击登录** - 自动点击登录按钮
7. **验证结果** - 检查登录是否成功

## 验证码识别

程序使用以下技术来提高验证码识别准确率：

- **图像预处理**：灰度化、二值化、去噪
- **图像增强**：放大图片提高清晰度
- **OCR识别**：使用Tesseract进行文字识别
- **结果清理**：过滤非字母数字字符

## 故障排除

### 常见问题

1. **ChromeDriver错误**
   - 确保Chrome浏览器已安装并更新到最新版本
   - Selenium 4.x会自动管理ChromeDriver

2. **Tesseract未找到**
   - 确保Tesseract已正确安装
   - Windows用户需要将Tesseract路径添加到PATH环境变量

3. **验证码识别失败**
   - 验证码图片质量可能较差
   - 可以尝试手动刷新验证码
   - 调整图像预处理参数

4. **元素定位失败**
   - 网站结构可能发生变化
   - 检查网页源码，更新元素选择器

### 调试模式

如需查看浏览器操作过程，请注释掉以下行：

```python
# chrome_options.add_argument("--headless")  # 注释这行可以显示浏览器窗口
```

## 注意事项

- ⚠️ 请确保有权限访问目标网站
- ⚠️ 验证码识别准确率取决于图片质量
- ⚠️ 网站结构变化可能导致程序失效
- ⚠️ 请遵守网站的使用条款和robots.txt
- ⚠️ 建议在测试环境中先验证程序功能

## 技术栈

- **Selenium** - 浏览器自动化
- **OpenCV** - 图像处理
- **Pillow** - 图像操作
- **Tesseract** - OCR文字识别
- **NumPy** - 数值计算

## 许可证

本项目仅供学习和研究使用，请勿用于非法用途。

## 更新日志

- v1.0.0 - 初始版本，支持基本登录和验证码识别功能

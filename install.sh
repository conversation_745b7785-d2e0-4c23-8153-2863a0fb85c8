#!/bin/bash

echo "🚀 自动登录程序安装脚本"
echo "=========================="

# 检查Python版本
echo "🐍 检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 升级pip
echo "🔄 升级pip..."
python3 -m pip install --upgrade pip

# 设置pip镜像源（解决网络问题）
echo "🌐 配置pip镜像源..."
pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip3 config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 逐个安装依赖包
echo "📦 开始安装依赖包..."

packages=(
    "selenium>=4.0.0"
    "pillow>=8.0.0"
    "numpy>=1.20.0"
    "opencv-python>=4.5.0"
    "pytesseract>=0.3.0"
)

failed_packages=()

for package in "${packages[@]}"; do
    echo "  📥 安装 $package..."
    if pip3 install "$package" --timeout 120; then
        echo "  ✅ $package 安装成功"
    else
        echo "  ❌ $package 安装失败"
        failed_packages+=("$package")
    fi
done

# 检查安装结果
if [ ${#failed_packages[@]} -eq 0 ]; then
    echo "✅ 所有依赖包安装完成！"
else
    echo "⚠️ 以下包安装失败:"
    for pkg in "${failed_packages[@]}"; do
        echo "   - $pkg"
    done
    echo "💡 您可以稍后手动安装这些包"
fi

echo ""
echo "=========================="
echo "📋 接下来的步骤:"
echo "1. 安装Tesseract OCR:"
echo "   macOS: brew install tesseract"
echo "   Ubuntu: sudo apt-get install tesseract-ocr"
echo ""
echo "2. 运行程序:"
echo "   python3 auto_login.py"
echo ""
echo "✨ 安装完成！"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装脚本 - 自动安装所需依赖
"""

import subprocess
import sys
import platform
import os


def install_requirements():
    """安装Python依赖包"""
    print("📦 正在安装Python依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Python依赖包安装失败: {e}")
        return False


def install_tesseract():
    """安装Tesseract OCR"""
    system = platform.system().lower()
    print(f"🔍 检测到操作系统: {system}")
    
    if system == "darwin":  # macOS
        print("🍎 macOS系统，请手动安装Tesseract:")
        print("   方法1: brew install tesseract")
        print("   方法2: brew install tesseract-lang")
        print("   如果没有brew，请先安装: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
    elif system == "linux":
        print("🐧 Linux系统，请手动安装Tesseract:")
        print("   Ubuntu/Debian: sudo apt-get install tesseract-ocr")
        print("   CentOS/RHEL: sudo yum install tesseract")
        print("   或者: sudo dnf install tesseract")
    elif system == "windows":
        print("🪟 Windows系统，请手动安装Tesseract:")
        print("   1. 下载安装包: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   2. 安装后将安装路径添加到系统PATH环境变量")
        print("   3. 默认安装路径通常是: C:\\Program Files\\Tesseract-OCR")
    
    return True


def install_chromedriver():
    """安装ChromeDriver说明"""
    print("🚗 ChromeDriver安装说明:")
    print("   现代版本的Selenium会自动管理ChromeDriver")
    print("   如果遇到问题，请确保Chrome浏览器已安装并更新到最新版本")
    print("   或者手动下载ChromeDriver: https://chromedriver.chromium.org/")
    return True


def main():
    """主安装流程"""
    print("🚀 开始安装自动登录程序依赖...")
    print("=" * 50)
    
    # 安装Python依赖
    if not install_requirements():
        print("❌ 安装失败，请检查错误信息")
        return False
    
    print("\n" + "=" * 50)
    
    # Tesseract安装说明
    install_tesseract()
    
    print("\n" + "=" * 50)
    
    # ChromeDriver安装说明
    install_chromedriver()
    
    print("\n" + "=" * 50)
    print("✅ 依赖安装完成！")
    print("\n📋 使用说明:")
    print("   1. 确保已安装Chrome浏览器")
    print("   2. 确保已安装Tesseract OCR")
    print("   3. 运行程序: python auto_login.py")
    print("\n⚠️  注意事项:")
    print("   - 首次运行可能需要下载ChromeDriver")
    print("   - 验证码识别准确率取决于图片质量")
    print("   - 如果登录失败，请检查网站结构是否有变化")
    
    return True


if __name__ == "__main__":
    main()

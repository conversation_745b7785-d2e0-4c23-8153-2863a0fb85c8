#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装脚本 - 自动安装所需依赖
"""

import subprocess
import sys
import platform
import os


def upgrade_pip():
    """升级pip到最新版本"""
    print("🔄 正在升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip升级完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️ pip升级失败，继续安装: {e}")
        return True  # 即使升级失败也继续


def install_requirements():
    """安装Python依赖包"""
    print("📦 正在安装Python依赖包...")

    # 定义依赖包列表，按重要性排序
    packages = [
        "selenium>=4.0.0",
        "pillow>=8.0.0",
        "numpy>=1.20.0",
        "opencv-python>=4.5.0",
        "pytesseract>=0.3.0"
    ]

    failed_packages = []

    for package in packages:
        try:
            print(f"  📥 安装 {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install",
                package, "--timeout", "60", "--retries", "3"
            ])
            print(f"  ✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ {package} 安装失败")
            failed_packages.append(package)

    if failed_packages:
        print(f"\n⚠️ 以下包安装失败: {', '.join(failed_packages)}")
        print("💡 建议手动安装:")
        for pkg in failed_packages:
            print(f"   pip3 install {pkg}")
        return len(failed_packages) == 0
    else:
        print("✅ 所有Python依赖包安装完成")
        return True


def install_tesseract():
    """安装Tesseract OCR"""
    system = platform.system().lower()
    print(f"🔍 检测到操作系统: {system}")

    if system == "darwin":  # macOS
        print("🍎 macOS系统，请手动安装Tesseract:")
        print("   方法1: brew install tesseract")
        print("   方法2: brew install tesseract-lang")
        print("   如果没有brew，请先安装: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
    elif system == "linux":
        print("🐧 Linux系统，请手动安装Tesseract:")
        print("   Ubuntu/Debian: sudo apt-get install tesseract-ocr")
        print("   CentOS/RHEL: sudo yum install tesseract")
        print("   或者: sudo dnf install tesseract")
    elif system == "windows":
        print("🪟 Windows系统，请手动安装Tesseract:")
        print("   1. 下载安装包: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   2. 安装后将安装路径添加到系统PATH环境变量")
        print("   3. 默认安装路径通常是: C:\\Program Files\\Tesseract-OCR")

    return True


def install_chromedriver():
    """安装ChromeDriver说明"""
    print("🚗 ChromeDriver安装说明:")
    print("   现代版本的Selenium会自动管理ChromeDriver")
    print("   如果遇到问题，请确保Chrome浏览器已安装并更新到最新版本")
    print("   或者手动下载ChromeDriver: https://chromedriver.chromium.org/")
    return True


def main():
    """主安装流程"""
    print("🚀 开始安装自动登录程序依赖...")
    print("=" * 50)

    # 升级pip
    upgrade_pip()

    print("\n" + "=" * 50)

    # 安装Python依赖
    success = install_requirements()
    if not success:
        print("⚠️ 部分依赖安装失败，但程序可能仍可运行")
        print("💡 您可以稍后手动安装失败的包")

    print("\n" + "=" * 50)

    # Tesseract安装说明
    install_tesseract()

    print("\n" + "=" * 50)

    # ChromeDriver安装说明
    install_chromedriver()

    print("\n" + "=" * 50)
    print("✅ 依赖安装完成！")
    print("\n📋 使用说明:")
    print("   1. 确保已安装Chrome浏览器")
    print("   2. 确保已安装Tesseract OCR")
    print("   3. 运行程序: python auto_login.py")
    print("\n⚠️  注意事项:")
    print("   - 首次运行可能需要下载ChromeDriver")
    print("   - 验证码识别准确率取决于图片质量")
    print("   - 如果登录失败，请检查网站结构是否有变化")

    return True


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能自动登录程序 - 解决验证码和登录按钮问题
"""

import time
import io
from PIL import Image
import pytesseract
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import cv2
import numpy as np


class SmartAutoLogin:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.url = "https://oa.tcjyxx.cn/dzt-oa/frame/fui/pages/themes/idea/idea?frameUrlSecretParam=cGFnZUlkJTNEb2Fwcm9kdWN0Z3hoLWlkZWElNDBlcG9pbnRfdXNlcl9sb2dpbmlkJTNENURDQzQyMjE3MDU1RTM0QjFBRDVDRjUyNUMwOEMwMUI3MDc4OEEzREQ5OUYyOUM5QjlGMkVGQzVDODg3OTVFQ0Q4N0Q1QUZFNkVBNERDMUMyRjg2MDM1NDgzNTlGNEQ1"
        self.username = "9CF61A77778C44EDAE5FE5B9C80BFEBE"
        self.password = "*Amkeo#770304"

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # 显示浏览器窗口以便观察
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动Chrome浏览器失败: {e}")
            return False

    def wait_for_page_load(self):
        """等待页面完全加载"""
        try:
            # 等待页面标题变化
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.title != "登录"
            )
            time.sleep(2)
        except:
            time.sleep(3)

    def fill_credentials(self):
        """填写用户名和密码"""
        print("📝 正在填写登录信息...")

        # 填写用户名
        try:
            username_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder='请输入用户名']"))
            )
            username_input.clear()
            username_input.send_keys(self.username)
            print("✅ 用户名填写完成")
        except:
            print("❌ 未找到用户名输入框")
            return False

        # 填写密码
        try:
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder='请输入密码']")
            password_input.clear()
            password_input.send_keys(self.password)
            print("✅ 密码填写完成")
        except:
            print("❌ 未找到密码输入框")
            return False

        return True

    def handle_captcha_smart(self):
        """智能处理验证码"""
        print("🔍 智能处理验证码...")

        # 查找验证码图片
        captcha_images = []
        img_selectors = [
            "img[id='code-img']",
            "img[id='phonecode-img']",
            "img[id='usbcode-img']",
            "img[src*='captcha']",
            "img[src*='code']"
        ]

        for selector in img_selectors:
            try:
                img = self.driver.find_element(By.CSS_SELECTOR, selector)
                if img.is_displayed():
                    captcha_images.append((img, selector))
                    print(f"✅ 找到验证码图片: {selector}")
            except:
                continue

        if not captcha_images:
            print("ℹ️ 未找到验证码图片")
            return True

        # 处理找到的验证码
        for img_element, img_selector in captcha_images:
            print(f"🎯 处理验证码图片: {img_selector}")

            # 识别验证码
            captcha_text = self.recognize_captcha(img_element)
            if not captcha_text:
                print("❌ 验证码识别失败")
                continue

            # 找到对应的输入框
            input_element = self.find_captcha_input(img_selector)
            if input_element:
                input_element.clear()
                input_element.send_keys(captcha_text)
                print(f"✅ 验证码填写完成: {captcha_text}")
                time.sleep(1)
                return True

        return False

    def find_captcha_input(self, img_selector):
        """根据验证码图片找到对应的输入框"""
        input_mappings = {
            "img[id='code-img']": "input[id='code-input']",
            "img[id='phonecode-img']": "input[id='phonecode-input']",
            "img[id='usbcode-img']": "input[id='usbcode-input']"
        }

        # 尝试映射关系
        if img_selector in input_mappings:
            try:
                input_elem = self.driver.find_element(By.CSS_SELECTOR, input_mappings[img_selector])
                if input_elem.is_displayed():
                    return input_elem
            except:
                pass

        # 通用查找
        general_selectors = [
            "input[placeholder*='验证码']",
            "input[id*='code']",
            "input[name*='captcha']"
        ]

        for selector in general_selectors:
            try:
                input_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                if input_elem.is_displayed():
                    return input_elem
            except:
                continue

        return None

    def recognize_captcha(self, captcha_element):
        """识别验证码"""
        try:
            # 获取验证码图片
            captcha_screenshot = captcha_element.screenshot_as_png
            captcha_image = Image.open(io.BytesIO(captcha_screenshot))

            # 预处理图片
            img_array = np.array(captcha_image)
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            # 二值化和放大
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            height, width = binary.shape
            binary = cv2.resize(binary, (width*3, height*3), interpolation=cv2.INTER_CUBIC)

            processed_image = Image.fromarray(binary)

            # OCR识别
            custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            captcha_text = pytesseract.image_to_string(processed_image, config=custom_config).strip()
            captcha_text = ''.join(c for c in captcha_text if c.isalnum())

            print(f"🔍 识别到验证码: {captcha_text}")
            return captcha_text

        except Exception as e:
            print(f"❌ 验证码识别失败: {e}")
            return ""

    def try_login_methods(self):
        """尝试多种登录方法"""
        print("🚀 尝试登录...")

        methods = [
            self.method_submit_button,
            self.method_enter_key,
            self.method_javascript_submit,
            self.method_form_submit
        ]

        for i, method in enumerate(methods, 1):
            print(f"🔄 尝试登录方法 {i}: {method.__name__}")
            if method():
                return True
            time.sleep(2)

        return False

    def method_submit_button(self):
        """方法1: 点击submit按钮"""
        try:
            # 查找submit按钮
            submit_btn = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")

            # 尝试启用按钮
            self.driver.execute_script("""
                arguments[0].removeAttribute('disabled');
                arguments[0].classList.remove('unable');
                arguments[0].classList.add('able');
                arguments[0].style.display = 'block';
                arguments[0].style.visibility = 'visible';
            """, submit_btn)

            # 滚动到按钮位置并点击
            self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
            time.sleep(1)
            submit_btn.click()

            print("✅ Submit按钮点击完成")
            return self.check_login_success()

        except Exception as e:
            print(f"❌ Submit按钮方法失败: {e}")
            return False

    def method_enter_key(self):
        """方法2: 使用回车键提交"""
        try:
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder='请输入密码']")
            password_input.send_keys(Keys.RETURN)
            print("✅ 回车键提交完成")
            return self.check_login_success()
        except Exception as e:
            print(f"❌ 回车键方法失败: {e}")
            return False

    def method_javascript_submit(self):
        """方法3: JavaScript提交表单"""
        try:
            # 查找表单并提交
            self.driver.execute_script("""
                var forms = document.getElementsByTagName('form');
                if (forms.length > 0) {
                    forms[0].submit();
                    return true;
                }

                // 尝试触发登录事件
                var submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.click();
                    return true;
                }

                return false;
            """)
            print("✅ JavaScript提交完成")
            return self.check_login_success()
        except Exception as e:
            print(f"❌ JavaScript方法失败: {e}")
            return False

    def method_form_submit(self):
        """方法4: 直接提交表单"""
        try:
            form = self.driver.find_element(By.TAG_NAME, "form")
            form.submit()
            print("✅ 表单提交完成")
            return self.check_login_success()
        except Exception as e:
            print(f"❌ 表单提交方法失败: {e}")
            return False

    def check_login_success(self):
        """检查登录是否成功"""
        time.sleep(3)
        current_url = self.driver.current_url
        print(f"当前URL: {current_url}")

        # 检查URL变化 - 新的URL结构
        if current_url != self.url:
            # 检查是否跳转到成功页面或主页面
            if any(keyword in current_url.lower() for keyword in ['success', 'main', 'index', 'home', 'dashboard']):
                print("🎉 登录成功！页面已跳转到成功页面")
                return True
            elif "login" not in current_url.lower():
                print("🎉 登录成功！页面已跳转")
                return True

        # 检查页面标题变化
        title = self.driver.title
        if "登录" not in title and title != "登录" and title.strip():
            print(f"🎉 登录成功！页面标题已变化: {title}")
            return True

        # 检查是否有主页面元素（如菜单、导航等）
        try:
            main_page_indicators = [
                "[class*='menu']",
                "[class*='nav']",
                "[class*='sidebar']",
                "[class*='header']",
                "[class*='main']",
                "[id*='menu']",
                "[id*='nav']"
            ]

            for selector in main_page_indicators:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if len(elements) > 0:
                    print("🎉 登录成功！发现主页面元素")
                    return True
        except:
            pass

        # 检查是否有错误信息
        try:
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error, .alert, [class*='error'], [class*='alert']")
            for elem in error_elements:
                if elem.is_displayed() and elem.text.strip():
                    print(f"⚠️ 发现错误信息: {elem.text}")
                    return False
        except:
            pass

        print("⚠️ 登录状态不明确，页面未明显变化")
        return False

    def click_official_mail(self):
        """点击公务邮件按钮"""
        print("📧 正在查找并点击'公务邮件'按钮...")

        # 等待页面完全加载
        time.sleep(3)

        # 多种选择器来查找"公务邮件"按钮
        mail_selectors = [
            "//*[contains(text(), '公务邮件')]",
            "//*[contains(text(), '邮件') and contains(text(), '公务')]",
            "//a[contains(text(), '公务邮件')]",
            "//button[contains(text(), '公务邮件')]",
            "//div[contains(text(), '公务邮件')]",
            "//span[contains(text(), '公务邮件')]",
            "//*[@title='公务邮件']",
            "//*[contains(@class, 'mail') and contains(text(), '公务')]",
            "//*[contains(@id, 'mail') and contains(text(), '公务')]"
        ]

        for i, selector in enumerate(mail_selectors):
            try:
                print(f"  🔍 尝试选择器 {i+1}/{len(mail_selectors)}: {selector}")
                element = self.driver.find_element(By.XPATH, selector)

                if element.is_displayed():
                    print(f"  ✅ 找到'公务邮件'按钮: {element.text}")

                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)

                    # 点击元素
                    element.click()
                    print("✅ '公务邮件'按钮点击完成")
                    return True
                else:
                    print(f"  ⚠️ 找到元素但不可见: {selector}")

            except Exception as e:
                print(f"  ❌ 选择器失败 {selector}: {str(e)[:100]}...")
                continue

        # 如果没有找到，尝试查找所有可能的邮件相关元素
        print("🔍 未找到明确的'公务邮件'按钮，查找所有邮件相关元素...")
        try:
            all_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '邮件')]")
            print(f"  📊 找到 {len(all_elements)} 个包含'邮件'的元素")

            for i, element in enumerate(all_elements):
                try:
                    if element.is_displayed():
                        text = element.text.strip()
                        tag = element.tag_name
                        print(f"    元素 {i+1}: 标签={tag}, 文本='{text}'")

                        if '公务' in text or '邮件' in text:
                            print(f"  🎯 尝试点击疑似邮件按钮: {text}")
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            element.click()
                            print("✅ 邮件按钮点击完成")
                            return True
                except:
                    continue

        except Exception as e:
            print(f"❌ 查找邮件元素时出错: {e}")

        print("❌ 未找到'公务邮件'按钮")
        return False

    def click_mail_submenu(self):
        """点击邮件子菜单中的'邮件'按钮"""
        print("📬 正在查找并点击子菜单中的'邮件'按钮...")

        # 等待子菜单出现
        time.sleep(2)

        # 多种选择器来查找子菜单中的"邮件"按钮
        submenu_selectors = [
            "//*[contains(text(), '邮件') and not(contains(text(), '公务'))]",
            "//a[contains(text(), '邮件') and not(contains(text(), '公务'))]",
            "//li[contains(text(), '邮件')]",
            "//div[contains(text(), '邮件') and not(contains(text(), '公务'))]",
            "//span[contains(text(), '邮件') and not(contains(text(), '公务'))]",
            "//*[@title='邮件']",
            "//*[contains(@class, 'menu') and contains(text(), '邮件')]",
            "//*[contains(@class, 'submenu') and contains(text(), '邮件')]",
            "//*[text()='邮件']"
        ]

        for i, selector in enumerate(submenu_selectors):
            try:
                print(f"  🔍 尝试选择器 {i+1}/{len(submenu_selectors)}: {selector}")
                elements = self.driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed():
                        element_text = element.text.strip()
                        print(f"  ✅ 找到'邮件'按钮: {element_text}")

                        # 确保这不是"公务邮件"按钮，而是子菜单中的"邮件"
                        if element_text == '邮件' or (element_text and '公务' not in element_text):
                            # 滚动到元素位置
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)

                            # 点击元素
                            element.click()
                            print("✅ 子菜单'邮件'按钮点击完成")
                            return True
                        else:
                            print(f"  ⚠️ 跳过非目标元素: {element_text}")

            except Exception as e:
                print(f"  ❌ 选择器失败 {selector}: {str(e)[:100]}...")
                continue

        # 如果没有找到，尝试查找所有新出现的邮件相关元素
        print("🔍 未找到明确的子菜单'邮件'按钮，查找所有新出现的元素...")
        try:
            # 查找所有可能的菜单项
            menu_elements = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'menu') or contains(@class, 'item') or contains(@class, 'link')]")

            for element in menu_elements:
                try:
                    if element.is_displayed():
                        text = element.text.strip()
                        if text == '邮件' or (text and '邮件' in text and '公务' not in text):
                            print(f"  🎯 尝试点击疑似邮件子菜单: {text}")
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            element.click()
                            print("✅ 邮件子菜单点击完成")
                            return True
                except:
                    continue

        except Exception as e:
            print(f"❌ 查找邮件子菜单时出错: {e}")

        print("❌ 未找到子菜单中的'邮件'按钮")
        return False

    def navigate_to_mail(self):
        """导航到邮件功能"""
        print("🧭 开始导航到邮件功能...")

        # 第一步：点击"公务邮件"按钮
        if not self.click_official_mail():
            print("❌ 无法点击'公务邮件'按钮")
            return False

        # 等待子菜单出现
        time.sleep(2)

        # 第二步：点击子菜单中的"邮件"按钮
        if not self.click_mail_submenu():
            print("❌ 无法点击子菜单中的'邮件'按钮")
            return False

        # 等待邮件页面加载
        time.sleep(3)

        # 验证是否成功进入邮件页面
        current_url = self.driver.current_url
        title = self.driver.title
        print(f"导航后的URL: {current_url}")
        print(f"导航后的标题: {title}")

        # 检查是否成功进入邮件页面
        if any(keyword in current_url.lower() for keyword in ['mail', '邮件']) or \
           any(keyword in title for keyword in ['邮件', 'mail']):
            print("🎉 成功导航到邮件页面！")
            return True
        else:
            print("⚠️ 邮件页面导航状态不明确")
            return True  # 即使不确定也返回True，让用户自己判断

    def run(self):
        """运行主程序"""
        print("🤖 智能自动登录程序启动")

        if not self.setup_driver():
            return

        try:
            # 打开登录页面
            print(f"🌐 正在打开网站: {self.url}")
            self.driver.get(self.url)
            self.wait_for_page_load()

            # 填写登录信息
            if not self.fill_credentials():
                print("❌ 填写登录信息失败")
                return

            # 处理验证码
            if not self.handle_captcha_smart():
                print("❌ 验证码处理失败")
                return

            # 尝试登录
            if self.try_login_methods():
                print("✅ 登录成功！")

                # 登录成功后导航到邮件功能
                print("\n" + "="*50)
                print("🚀 开始导航到邮件功能...")
                print("="*50)

                if self.navigate_to_mail():
                    print("🎉 邮件功能导航完成！")
                else:
                    print("⚠️ 邮件功能导航可能未完全成功，但程序已尽力尝试")

            else:
                print("❌ 所有登录方法都失败了")

            input("\n按回车键关闭浏览器...")

        except Exception as e:
            print(f"❌ 程序运行出错: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")


if __name__ == "__main__":
    smart_login = SmartAutoLogin()
    smart_login.run()

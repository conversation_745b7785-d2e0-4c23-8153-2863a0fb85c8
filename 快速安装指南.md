# 🚀 快速安装指南

## ✅ 安装状态
- ✅ Python依赖包已安装完成
- ✅ Tesseract OCR已安装完成
- ✅ 程序已准备就绪

## 🎯 现在可以运行程序

```bash
python3 auto_login.py
```

## 📋 已安装的组件

### Python依赖包
- ✅ selenium (浏览器自动化)
- ✅ pillow (图像处理)
- ✅ opencv-python (图像预处理)
- ✅ pytesseract (OCR文字识别)
- ✅ numpy (数值计算)

### 系统组件
- ✅ Tesseract OCR 5.5.1 (验证码识别引擎)

## 🔧 程序配置

程序已预配置以下信息：
- **网站**: https://oa.tcjyxx.cn/dzt-sso/
- **用户名**: 9CF61A77778C44EDAE5FE5B9C80BFEBE
- **密码**: *Amkeo#770304

## 🚀 运行程序

1. **启动程序**:
   ```bash
   python3 auto_login.py
   ```

2. **程序将自动**:
   - 打开Chrome浏览器
   - 访问登录页面
   - 填写用户名和密码
   - 识别并填写验证码（如果有）
   - 点击登录按钮

## ⚠️ 注意事项

- 确保Chrome浏览器已安装
- 首次运行可能需要下载ChromeDriver
- 验证码识别准确率取决于图片质量
- 如果登录失败，程序会显示错误信息

## 🛠️ 故障排除

如果遇到问题，请检查：

1. **Chrome浏览器**: 确保已安装最新版本
2. **网络连接**: 确保能正常访问目标网站
3. **权限问题**: 确保程序有权限访问浏览器

## 📞 技术支持

如果程序运行出现问题，请查看控制台输出的错误信息，或检查README.md文件中的详细故障排除指南。
